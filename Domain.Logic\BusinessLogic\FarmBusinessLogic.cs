using Domain.Entities.Model;
using Binit.Framework.Constants.SeedEntities;
using Domain.Logic.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Binit.Framework.Interfaces.DAL;
using Microsoft.EntityFrameworkCore;
using Domain.Entities.Model.Enum;
using DAL.Interfaces;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework;
using Microsoft.Extensions.Localization;
using Lang = Binit.Framework.Localization.LocalizationConstants.DomainLogic.BusinessLogic.FarmBusinessLogic;
using Binit.Framework.Interfaces.ExceptionHandling;
using Domain.Logic.DTOs.FarmDTOs.FarmItemSummaryDTO;
using Domain.Logic.DTOs.HenWarehouseDTOs.HenWarehouseItem;
using Domain.Logic.DTOs.FarmDTOs.FarmItemDTO;
using Domain.Logic.BusinessLogic.DTOs;
using Binit.Framework.Helpers;
using Domain.Entities.Model.Views;
using Binit.Shaper.Entities.Alias;

namespace Domain.Logic.BusinessLogic
{
    public class FarmBusinessLogic : IFarmBusinessLogic
    {
        private readonly IUserService<ApplicationUser> userService;
        private readonly IFarmService farmService;
        private readonly IClusterService clusterService;
        private readonly IClassificationWarehouseService classificationWarehouseService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IOperationContext operationContext;
        private readonly IUnitOfWork unitOfWork;
        private readonly IExceptionManager exceptionManager;
        private readonly IContainerService<Container> containerService;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportService henReportService;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly ISiloService siloService;
        private readonly IStorageWarehouseService storageWarehouseService;
        private readonly ILineService lineService;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IIgniteAddressService igniteAddressService;
        private readonly ICompanyService companyService;

        public FarmBusinessLogic(
            IUserService<ApplicationUser> userService,
            IFarmService farmService,
            IClusterService clusterService,
            IClassificationWarehouseService classificationWarehouseService,
            IStringLocalizer<SharedResources> localizer,
            IService<TenantConfiguration> tenantConfigurationService,
            IOperationContext operationContext,
            IUnitOfWork unitOfWork,
            IExceptionManager exceptionManager,
            IContainerService<Container> containerService,
            IHenBatchService henBatchService,
            IHenReportService henReportService,
            IHenWarehouseService henWarehouseService,
            ISiloService siloService,
            IStorageWarehouseService storageWarehouseService,
            ILineService lineService,
            IMaterialTypeService materialTypeService,
            IIgniteAddressService igniteAddressService,
            ICompanyService companyService)
        {
            this.userService = userService;
            this.farmService = farmService;
            this.clusterService = clusterService;
            this.classificationWarehouseService = classificationWarehouseService;
            this.localizer = localizer;
            this.tenantConfigurationService = tenantConfigurationService;
            this.operationContext = operationContext;
            this.unitOfWork = unitOfWork;
            this.exceptionManager = exceptionManager;
            this.containerService = containerService;
            this.henBatchService = henBatchService;
            this.henReportService = henReportService;
            this.henWarehouseService = henWarehouseService;
            this.siloService = siloService;
            this.storageWarehouseService = storageWarehouseService;
            this.lineService = lineService;
            this.materialTypeService = materialTypeService;
            this.igniteAddressService = igniteAddressService;
            this.companyService = companyService;
        }

        /// <summary>
        /// The logic when creating a new farm, adds it to the list of farms of the technician.
        /// </summary>
        public async Task CreateAsync(Farm farm)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await AsyncLogic();
            else await unitOfWork.ExecuteAsTransactionAsync(AsyncLogic);

            async Task AsyncLogic()
            {
                await this.farmService.CreateAsync(farm);

               
               

                // If the tenant is configured to not have clusters, generate one automatically when creating the farm.
                bool tenantHasClusters = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId())
                    .Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");

                if (!tenantHasClusters)
                {
                    await clusterService.CreateAsync(new Cluster()
                    {
                        Id = new Guid(),
                        Name = farm.Name,
                        CompanyId = farm.CompanyId,
                        FarmId = farm.Id
                    });
                }
                Silo silo = new Silo();
                silo.Id = new Guid();
                silo.DetailedName = "Silo de recebimento de ração";
                silo.Name = "Silo Transitório";
                silo.Number = 999;
                silo.ContainerType = "silo";
                silo.Code = farm.Code;
                silo.FarmId = farm.Id;
                silo.Active = true;
                silo.Aliases = new List<Alias>();

                silo.AcceptedMaterialType = new List<ContainerMaterialType>();
                silo.AcceptedMaterialType.Add(new ContainerMaterialType
                {
                    ContainerId = silo.Id,
                    MaterialTypeId = MaterialTypes.InsumoMateriaPrimaAlimentacionFormula,
                    ActionEnum = ActionsEnum.ConsumeAndProduce,
                    CapacityStandarizedValue = 300000,
                    CapacityUnitId = CapacityUnits.Kilograms,
                    AllowedCapacity = 300000,
                    MaximumCapacity = 300000,
                    RecommendedCapacity = 300000
                });                            

            await siloService.CreateAsync(silo);
            }
        }

        /// <summary>
        /// Creates the first part of the structure of the farm. That means it creates farm, silos and warehouses
        /// </summary>
        public async Task CreateFarmStructure(Farm farm, List<Silo> silos, List<StorageWarehouseStructureDTO> storageWarehouses, List<Cluster> clusters)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await CreateStructureAsync();
            else await unitOfWork.ExecuteAsTransactionAsync(CreateStructureAsync);

            async Task CreateStructureAsync()
            {
                await this.CreateAsync(farm);
                Company company = farm.CompanyId.HasValue ? companyService.Get(farm.CompanyId.Value) : null;

                foreach (Silo silo in silos)
                    await containerService.CreateAsync(SetFarmProperties(silo, farm, company) as Silo);


                List<Guid> classificationWarehousesIds = storageWarehouses.Where(sw => sw.ClassificationWarehouseId.HasValue).Select(sw => sw.ClassificationWarehouseId.Value).ToList();
                List<ClassificationWarehouse> classificationWarehouses = classificationWarehouseService.GetAllIgnoringClaims()
                    .Include(c => c.OriginContainers)
                    .Where(c => classificationWarehousesIds.Any(id => id == c.Id)).ToList();
                foreach (StorageWarehouseStructureDTO dto in storageWarehouses)
                {
                    dto.storageWarehouse.Code = farm.Code + "-" + dto.storageWarehouse.Code;
                    await storageWarehouseService.CreateAsync(SetFarmProperties(dto.storageWarehouse, farm, company) as StorageWarehouse);
                }

                foreach (var group in storageWarehouses.Where(sw => sw.ClassificationWarehouseId.HasValue).GroupBy(sw => sw.ClassificationWarehouseId))
                    await UpdateClassificationWarehouse(group.Select(sw => sw.storageWarehouse.Id).ToList(), classificationWarehouses.FirstOrDefault(cw => cw.Id == group.Key));

                foreach (Cluster cluster in clusters)
                {
                    cluster.FarmId = farm.Id;
                    cluster.CompanyId = farm.CompanyId;
                    await clusterService.CreateAsync(cluster);
                }

            }
        }

        /// <summary>
        /// Sets farm properties to every container of the farm 
        /// </summary>
        public Container SetFarmProperties(Container container, Farm farm, Company company)
        {
            container.FarmId = farm.Id;
            container.AddressId = farm.AddressId;
            container.CompanyId = company.Id;
            container.Company = company;
            return container;
        }

        /// <summary>
        /// Updates the origins of the classification warehouse, according to farm structure 
        /// </summary>
        public async Task UpdateClassificationWarehouse(List<Guid> sw, ClassificationWarehouse classificationWarehouse)
        {
            classificationWarehouse.OriginContainers.AddRange(sw.Select(id => new ContainerContainer() { ContainerId = classificationWarehouse.Id, OriginId = id }));
            await classificationWarehouseService.UpdateAsync(classificationWarehouse);
        }

        /// <summary>
        /// Updates the origins of the classification warehouses, according to farm structure 
        /// </summary>
        public async Task UpdateClassificationWarehouses(Guid sw, ClassificationWarehouse newClassificationWarehouse, ClassificationWarehouse oldClassificationWarehouse)
        {
            if (oldClassificationWarehouse != null && oldClassificationWarehouse.Id != newClassificationWarehouse.Id)
            {
                oldClassificationWarehouse.OriginContainers = oldClassificationWarehouse.OriginContainers.Where(oc => oc.OriginId != sw).ToList();
                await classificationWarehouseService.UpdateAsync(oldClassificationWarehouse);
            }
            if (oldClassificationWarehouse == null || (oldClassificationWarehouse != null && oldClassificationWarehouse.Id != newClassificationWarehouse.Id))
            {
                newClassificationWarehouse.OriginContainers.Add(new ContainerContainer() { ContainerId = newClassificationWarehouse.Id, OriginId = sw });
                await classificationWarehouseService.UpdateAsync(newClassificationWarehouse);
            }
        }

        /// <summary>
        /// Creates the second part of the structure of the farm. That means it creates hen warehouses and lines 
        /// </summary>
        public async Task CreateFarmWarehousesAndLines(Guid farmId, List<HenWarehouseStructureDTO> warehouses)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await CreateStructureAsync();
            else await unitOfWork.ExecuteAsTransactionAsync(CreateStructureAsync);

            async Task CreateStructureAsync()
            {
                Farm farm = farmService.Get(farmId);
                
                Company company = farm.CompanyId.HasValue ? companyService.Get(farm.CompanyId.Value) : null;
                
                List<Guid> storageWarehousesIds = warehouses.Select(w => w.StorageWarehouse).ToList();
                
                List<Container> sorageWarehouses = containerService.GetAllIgnoringClaims()
                    .Where(c => c.ContainerType == ContainerTypes.StorageWarehouse && storageWarehousesIds.Any(w => w == c.Id))
                    .Include(c => c.OriginContainers)
                    .ToList();

                HashSet<Guid> slaughterhousesIds = warehouses.SelectMany(w => w.Slaughterhouse).ToHashSet();

                List<Container> slaughterhouses = containerService.GetAllIgnoringClaims()
                    .Where(c => c.ContainerType == ContainerTypes.Slaughterhouse && slaughterhousesIds.Contains(c.Id))
                    .Include(c => c.OriginContainers)
                    .ToList();

                foreach (var dto in warehouses)
                {
                    await henWarehouseService.CreateAsync(SetFarmProperties(dto.HenWarehouse, farm, company) as HenWarehouse);

                    List<Guid> linesIds = new List<Guid>();
                    foreach (Line line in dto.Lines)
                    {
                        SetLineValues(line, dto);
                        await lineService.CreateAsync(SetFarmProperties(line, farm, company) as Line);
                        linesIds.Add(line.Id);
                    }

                    if (dto.StorageWarehouse != Guid.Empty)
                        await UpdateOrigins(sorageWarehouses.FirstOrDefault(c => c.Id == dto.StorageWarehouse), linesIds);

                    foreach (var sh in dto.Slaughterhouse)
                    {
                        await UpdateOrigins(slaughterhouses.FirstOrDefault(c => c.Id == sh), linesIds);
                    }
                }
            }
        }

        private async Task UpdateOrigins(Container container, List<Guid> linesIds)
        {
            container.OriginContainers.AddRange(linesIds.Select(l => new ContainerContainer() { OriginId = l }));
            await containerService.UpdateAsync(container);
        }

        /// <summary>
        /// Updates the second part of the structure of the farm. That means it updates hen warehouses and lines 
        /// </summary>
        public async Task UpdateFarmWarehousesAndLines(FarmWarehousesAndLinesStructureDTO dto)
        {
            if (unitOfWork.GetModelDbContext().Database.CurrentTransaction != null) await UpdateStructureAsync();
            else await unitOfWork.ExecuteAsTransactionAsync(UpdateStructureAsync);

            async Task UpdateStructureAsync()
            {
                Farm farm = farmService.Get(dto.FarmId);
                Company company = farm.CompanyId.HasValue ? companyService.Get(farm.CompanyId.Value) : null;

                List<Line> lines = lineService.GetAll(asNoTracking: true).Where(l => l.Warehouse.FarmId == dto.FarmId).ToList();

                // update warehouses 
                foreach (HenWarehouseStructureDTO warehouse in dto.HenWarehouses)
                {
                    List<Line> dbLines = lines.Where(l => l.WarehouseId == warehouse.HenWarehouse.Id).ToList();
                    try
                    {
                        List<Guid> linesGuids = UpdateLines(dbLines, warehouse, farm, company).Result;

                        await henWarehouseService.UpdateAsync(SetFarmProperties(warehouse.HenWarehouse, farm, company) as HenWarehouse);
                        if (warehouse.StorageWarehouse != Guid.Empty)
                            await UpdateStorageWarehouses(linesGuids, warehouse.StorageWarehouse, ContainerTypes.StorageWarehouse);

                        if (!warehouse.Slaughterhouse.Any())
                            await UpdateStorageWarehouses(linesGuids, null, ContainerTypes.Slaughterhouse);
                        else
                            await UpdateSlaughterhouses(linesGuids, warehouse.Slaughterhouse);
                    }
                    catch (Exception ex) {
                        throw ex;
                    }
                }

                    // delete warehouse 
                    foreach (Guid warehouseId in dto.DeletedHenWarehouses)
                    {
                        List<Line> dbLines = lines.Where(l => l.WarehouseId == warehouseId).ToList();
                    try
                    {
                        List<Guid> linesGuids = UpdateLines(dbLines, new HenWarehouseStructureDTO(warehouseId), farm, company).Result;

                        await henWarehouseService.DeleteAsync(warehouseId);
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    }

                // create new warehouses 
                foreach (HenWarehouseStructureDTO warehouse in dto.NewHenWarehouses)
                {
                    await henWarehouseService.CreateAsync(SetFarmProperties(warehouse.HenWarehouse, farm, company) as HenWarehouse);
                    try {
                        List<Guid> linesGuids = UpdateLines(new List<Line>(), warehouse, farm, company).Result;

                        if (warehouse.StorageWarehouse != Guid.Empty)
                            await UpdateStorageWarehouses(linesGuids, warehouse.StorageWarehouse, ContainerTypes.StorageWarehouse);

                        foreach (var slaughterhouse in warehouse.Slaughterhouse)
                            await UpdateStorageWarehouses(linesGuids, slaughterhouse, ContainerTypes.Slaughterhouse);
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
                
            }
        }

        /// <summary>
        ///  Updates storage warehouses origins according to the edited lines 
        /// </summary>
        private async Task UpdateStorageWarehouses(List<Guid> lines, Guid? newContainer, string containerType)
        {
            IQueryable<Container> containers = containerService.GetAllIgnoringClaims()
                .Include(sw => sw.OriginContainers)
                .Where(sw => (sw.Id == newContainer || sw.OriginContainers.Any(oc => lines.Contains(oc.OriginId))) && sw.ContainerType == containerType);
            
            // if there are more than one storage that means that the storage has change, so its necessary to 
            // remove the lines from the old storage and add them to the new one. 
            if (containers.Count() > 1 || (!newContainer.HasValue && containers.Any()))
            {
                Container oldContainer = containers.FirstOrDefault(sw => sw.Id != newContainer);
                oldContainer.OriginContainers = oldContainer.OriginContainers.Where(oc => !lines.Contains(oc.OriginId)).ToList();
                await containerService.UpdateAsync(oldContainer);
            }

            if (newContainer.HasValue)
            {
                // for the selected stroage avoid adding lines that are already a origin. 
                Container newC = containers.FirstOrDefault(sw => sw.Id == newContainer);
                lines = lines.Where(l => !newC.OriginContainers.Any(oc => oc.OriginId == l)).ToList();
                if (lines.Any())
                {
                    newC.OriginContainers.AddRange(lines.Select(l => new ContainerContainer() { OriginId = l }));
                    await containerService.UpdateAsync(newC);
                }
            }
        }

        /// <summary>
        /// update, create o delete lines from an existing warehouse
        /// </summary>
        private async Task<List<Guid>> UpdateLines(List<Line> dbLines, HenWarehouseStructureDTO warehouse, Farm farm, Company company)
        {
            List<Guid> linesIds = new List<Guid>();
            try
            {
                // Create any lines that exist in the new lines but not in dbLines.
                foreach (Line line in warehouse.Lines.Except(dbLines, new EntityComparer<Line>()))
                {
                    SetLineValues(line, warehouse);
                    await lineService.CreateAsync(SetFarmProperties(line, farm, company) as Line);
                    linesIds.Add(line.Id);
                }

                // Update any line that exist both in newTasks and in dbTasks
                foreach (Line line in warehouse.Lines.Intersect(dbLines, new EntityComparer<Line>()))
                {
                    SetLineValues(line, warehouse);
                    await lineService.UpdateLineAsync(SetFarmProperties(line, farm, company) as Line,false);
                    linesIds.Add(line.Id);
                }

                // Remove any lines that exist in dbLines but not in the new lines.
                foreach (Line line in dbLines.Except(warehouse.Lines, new EntityComparer<Line>()))
                    await lineService.DeleteAsync(line.Id);
            }
            catch (ValidationException ex) {
                string message = "";
                foreach (var error in ex.Errors)
                    message = error.Value + " | ";
                throw new Exception(message);
            }
            return linesIds;
        }

        /// <summary>
        ///  Updates slaughterhouses origins according to the edited lines 
        /// </summary>
        private async Task UpdateSlaughterhouses(List<Guid> lines, List<Guid> slaughterhouses)
        {
            IQueryable<Container> containers = containerService.GetAllIgnoringClaims()
                .Include(sw => sw.OriginContainers)
                .Where(sw => sw.ContainerType == ContainerTypes.Slaughterhouse);

            // look for the symmetric difference to correctly update the containers
            IQueryable<Container> currents = containers.Where(sw => slaughterhouses.Contains(sw.Id));
            IQueryable<Guid> currentIds = currents.Select(sw => sw.Id);

            IQueryable<Container> olds = containers.Where(sw => sw.OriginContainers.Any(oc => lines.Contains(oc.OriginId)));
            IQueryable<Guid> oldIds = olds.Select(sw => sw.Id);

            List<Container> currentToUpdate = currents.Where(sw => !oldIds.Any(x => x == sw.Id)).ToList();
            List<Container> oldsToUpdate = olds.Where(sw => !currentIds.Any(x => x == sw.Id)).ToList();

            // reference to OriginContainers is removed
            foreach (var old in oldsToUpdate)
            {
                old.OriginContainers = old.OriginContainers.Where(oc => !lines.Contains(oc.OriginId)).ToList();
                await containerService.UpdateAsync(old);
            }

            // reference to the OriginContainers is added
            foreach (var current in currentToUpdate)
            {
                lines = lines.Where(l => !current.OriginContainers.Any(oc => oc.OriginId == l)).ToList();
                if (lines.Any())
                {
                    current.OriginContainers.AddRange(lines.Select(l => new ContainerContainer() { OriginId = l }));
                    await containerService.UpdateAsync(current);
                }
            }
        }

        /// <summary>
        /// sets line warehouse values 
        /// </summary>
        private void SetLineValues(Line line, HenWarehouseStructureDTO warehouse)
        {
            line.AreaContainers = warehouse.HenWarehouse.AreaContainers.Select(ac => new AreaContainer() { AreaEnum = ac.AreaEnum }).ToList();
            line.OriginContainers = warehouse.Silos.Select(s => new ContainerContainer() { OriginId = s }).ToList();
            line.WarehouseId = warehouse.HenWarehouse.Id;
        }

        /// <summary>
        /// The logic when Edit the farm, update the list of farms of the old and new Technician/Supervisor/Sanitarian.
        /// </summary>
        public async Task UpdateAsync(Farm farm)
        {
            TryUpdateDependencyAccesses(farm);

            await this.farmService.UpdateAsync(farm);
        }

        #region Update Dependencies

        private void TryUpdateDependencyAccesses(Farm farm)
        {
            Farm oldFarm = farmService.Get(farm.Id);

            IQueryable<ApplicationUser> allTechnicians = userService.GetAll(asNoTracking: true)
                                   .Include(u => u.SecurityProfiles).ThenInclude(usp => usp.SecurityProfile)
                                   .Include(u => u.Sites).ThenInclude(c => c.Site)
                                   .Include(u => u.Companies).ThenInclude(c => c.Company);

            // It is verified that the old Technician/Supervisor/Sanitarian do not have as their only farm
            // because updating this would have access to all farms
            ApplicationUser oldTechnician = allTechnicians.Where(u => u.Id == oldFarm.TechnicianId && u.Sites.Any(su => su.SiteId == farm.Id)).FirstOrDefault();
            if (oldTechnician != null && oldTechnician.Sites.Count() == 1)
                throw new ValidationException(nameof(farm.TechnicianId), this.localizer[Lang.PermissionsCanNotBeRemoved]);

            ApplicationUser oldSupervisor = allTechnicians.Where(u => u.Id == oldFarm.SupervisorId && u.Sites.Any(su => su.SiteId == farm.Id)).FirstOrDefault();
            if (oldSupervisor != null && oldSupervisor.Sites.Count() == 1)
                throw new ValidationException(nameof(farm.SupervisorId), this.localizer[Lang.PermissionsCanNotBeRemoved]);

            ApplicationUser oldSanitarian = allTechnicians.Where(u => u.Id == oldFarm.SanitarianId && u.Sites.Any(su => su.SiteId == farm.Id)).FirstOrDefault();
            if (oldSanitarian != null && oldSanitarian.Sites.Count() == 1)
                throw new ValidationException(nameof(farm.SanitarianId), this.localizer[Lang.PermissionsCanNotBeRemoved]);

        }

        #endregion

        /// <summary>
        /// The logic when delete the farm, remove it to the list of farms of the technician .
        /// </summary>
        public async Task DeleteAsync(Guid id)
        {
            if (userService.GetAll().Any(u => u.Sites.Any(s => s.SiteId == id)))
                throw new ValidationException(localizer[Lang.UserAssociatedToFarmEx]);

            ApplicationUser oldTechnician = userService.GetAll()
                                                .Include(u => u.SecurityProfiles).ThenInclude(usp => usp.SecurityProfile)
                                                .Include(u => u.Sites).ThenInclude(c => c.Site)
                                                .Where(u => u.Sites.Any(su => su.SiteId == id)).FirstOrDefault();
            if (oldTechnician != null)
            {
                List<SiteUser> listSites = oldTechnician.Sites;
                SiteUser siteUser = listSites.Where(s => s.SiteId == id).FirstOrDefault();
                listSites.Remove(siteUser);
                oldTechnician.Sites = listSites;
            }

            Farm farm = this.farmService.GetAll()
                                        .Include(f => f.Clusters)
                                        .Include(f => f.Sectors)
                                        .Where(f => f.Id == id)
                                        .FirstOrDefault();

            if(farm.Active)
                throw this.exceptionManager.Handle(new UserException(this.localizer[Lang.CanNotDeleteAnActiveFarm]));

            bool tenantHasClusters = tenantConfigurationService.GetAll()
                                                               .Where(t => t.TenantId == operationContext.GetUserTenantId())
                                                               .Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");


            ////Check if there's any cluster
            if (tenantHasClusters && farm.Clusters.Any())
                throw exceptionManager.Handle(new UserException(this.localizer[Lang.DeleteWithRelatedCluster]));

            bool hasFarmId = containerService.GetAll().Any(c => c.FarmId == id);

            if (!tenantHasClusters && !hasFarmId && farm.Clusters.Any())
            {
                Guid clusterIds = farm.Clusters.Select(c => c.Id).FirstOrDefault();
                await this.clusterService.DeleteAsync(clusterIds);
            }
            else if (hasFarmId)
                throw exceptionManager.Handle(new UserException(this.localizer[Lang.DeleteWithRelatedContainer]));

            //Check if there's any sector
            if (farm.Sectors.Any())
                throw exceptionManager.Handle(new UserException(this.localizer[Lang.DeleteWithRelatedSector]));

            await this.farmService.DeleteAsync(id);
        }
        /// <summary>
        /// Gets a farm by Id including all of its relationships needed for API's summary.
        /// </summary>
        public FarmItemSummaryDTO GetSummary(Guid id)
        {
            Farm farm = farmService.GetForAPISummary(id);

            IQueryable<Container> warehouses = containerService.GetAll()
                                                .Where(c => c.FarmId == id && c.ContainerType == ContainerTypes.HenWarehouse);

            IQueryable<HenBatch> henBatches = henBatchService.GetAll()
                                                 .Where(hb => hb.FarmId == id && hb.Active && hb.ParentId == null);

            decimal availableFormulas = siloService.GetAll()
                                .Where(s => s.FarmId == id)
                                .SelectMany(s => s.MaterialContainers.Where(mc => mc.Material.MaterialType.Path == MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula))
                                .Sum(m => m.Quantity);

            int users = userService.GetAllWithCompaniesAndFarms()
                                        .Where(u => u.Sites.Any(s => s.SiteId == id))
                                        .Count();

            return new FarmItemSummaryDTO(farm, warehouses, henBatches, availableFormulas, users);
        }

        /// <summary>
        /// Returns a list of henWarehouses corresponding to a given Farm with henwarehouse item format.
        /// </summary>
        public List<HenWarehouseItem> GetFarmHenWarehousesForSelect(Guid farmId)
        {
            return henWarehouseService.GetAll()
                                      .Where(hw => hw.FarmId == farmId
                                                   && hw.Lines.Any(l => l.HenBatches.Any(hb => hb.HenAmountFemale > 0 || hb.HenAmountMale > 0)))
                                      .OrderBy(c => c.Name)
                                      .Select(hw => new HenWarehouseItem(hw))
                                      .ToList();
        }

        /// <summary>
        /// Returns a list of farms that contains at least one warehouse with area laying
        /// </summary>
        public IQueryable<Farm> GetFarmsWithLayingHenWarehouse()
        {
            List<Guid?> farmIds = henWarehouseService.GetAll()
                                            .Include(hw => hw.AreaContainers)
                                            .Include(hw => hw.Lines).ThenInclude(l => l.HenBatches)
                                            .Where(hw => hw.AreaContainers.Any(ac => ac.AreaEnum == AreaEnum.Laying)
                                                    && hw.Lines.Any() && hw.Lines.Any(l => l.HenBatches.Any()))
                                            .Select(hw => hw.FarmId)
                                            .Distinct()
                                            .ToList();
            return farmService.GetAll().Where(f => farmIds.Any(fid => fid == f.Id));
        }

        /// <summary>
        /// Return id of receptionWarehhouse of the farm of a given henbatch
        /// </summary>
        public Guid? FarmHasReceptionWarehouse(Guid parentId)
        {
            Guid? farmId = henBatchService.GetAll()
                                        .Where(hb => hb.Id == parentId)
                                        .Select(hb => hb.FarmId).FirstOrDefault();

            IQueryable<Container> receptionWarehouse = containerService.GetAll()
                                    .Where(c => c.ContainerType == ContainerTypes.ReceptionWarehouse && c.FarmId == farmId);

            Guid? receptionWarehouseId = receptionWarehouse.Any() ? receptionWarehouse.Select(rw => rw.Id).FirstOrDefault() : (Guid?)null;

            return receptionWarehouseId;
        }

        /// <summary>
        /// Returns a list of henwarehouses for each farm, for offline mode.
        /// </summary>
        public List<HenWarehouseItem> GetFullHenWarehouses()
        {
            return henWarehouseService.GetAll()
                                      .Where(hw => hw.Lines.Any(l => l.HenBatches.Any(hb => hb.HenAmountFemale > 0 || hb.HenAmountMale > 0)))
                           .OrderBy(c => c.Name)
                           .Select(hw => new HenWarehouseItem(hw))
                           .ToList();
        }

        /// <summary>
        ///Change the state of the Farm.
        /// </summary>
        public async Task ChangeStateAsync(Guid id)
        {
            Farm farm = farmService.GetAll().Where(f => f.Id == id).FirstOrDefault();

            if (farm.Active)
            {
                IQueryable<HenBatch> henBatches = henBatchService.GetAll()
                .Where(hb => hb.FarmId == id);

                if (henBatches.Any(hb => hb.Active))
                    throw this.exceptionManager.Handle(new UserException(this.localizer[Lang.CantDeactivateFarm]));
            }

            farm.Active = !farm.Active;

            await this.farmService.UpdateAsync(farm);
        }

        /// <summary>
        /// Get a list of farms with details data for offline functionality
        /// </summary>
        public async Task<FullFarmPage> GetFullFarmPage(int page = 1, int pageSize = 0)
        {
            IQueryable<Farm> query = farmService.GetAll(asNoTracking: true);

            if (pageSize > 0)
                query = query.Skip((page - 1) * pageSize).Take(pageSize);

            List<Farm> farms = await query.ToListAsync();
            List<Guid> farmIds = farms.Select(f => f.Id).ToList();
            List<Container> warehouses = await containerService.GetAll()
                                                .Where(c => c.ContainerType == ContainerTypes.HenWarehouse && c.FarmId.HasValue && farmIds.Contains(c.FarmId.Value)).ToListAsync();

            List<HenBatch> henBatches = await henBatchService.GetAll()
                                                 .Where(hb => hb.Active && hb.ParentId == null && hb.FarmId.HasValue && farmIds.Contains(hb.FarmId.Value)).ToListAsync();

            List<Silo> availableFormulas = await siloService.GetAll()
                                .Where(s => s.FarmId.HasValue && farmIds.Contains(s.FarmId.Value)).ToListAsync();

            var containerDB = this.unitOfWork.GetDbContext<Container>();
            List<ContainerMaterialType> acceptedMaterialTypes = await containerDB.Set<ContainerMaterialType>().Where(c => warehouses.Select(w => w.Id).Contains(c.ContainerId) || availableFormulas.Select(s => s.Id).Contains(c.ContainerId)).ToListAsync();

            List<MaterialType> materialTypes = await materialTypeService.GetAll().Where(mt => acceptedMaterialTypes.Select(mt => mt.MaterialTypeId).Contains(mt.Id) && (mt.Path == MaterialTypePaths.ActivoBiologicoProductivoAve || mt.Path == MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula)).ToListAsync();

            List<IgniteAddress> addresses = await igniteAddressService.GetAll().Where(a => farms.Select(f => f.AddressId).Contains(a.Id)).ToListAsync();

            List<Company> companies = await companyService.GetAll().Where(c => farms.Select(f => f.CompanyId).Contains(c.Id)).ToListAsync();

            List<ApplicationUser> users = await userService.GetAll(asNoTracking: true).ToListAsync();

            IEnumerable<FarmItemSummaryDTO> fullFarmList = farms
                .Select(f =>
                new FarmItemSummaryDTO(
                                f,
                                companies.Where(c => f.CompanyId == c.Id).FirstOrDefault(),
                                users,
                                f.AddressId.HasValue ? addresses.Where(a => a.Id == f.AddressId.Value).FirstOrDefault() : null,
                                acceptedMaterialTypes.Where(cmt => warehouses.Where(wh => wh.FarmId.HasValue && wh.FarmId.Value == f.Id).Select(wh => wh.Id).Contains(cmt.ContainerId) && materialTypes.Where(mt => mt.Path == MaterialTypePaths.ActivoBiologicoProductivoAve).Any(mt => mt.Id == cmt.MaterialTypeId))
                                .Sum(cmt => cmt.CapacityStandarizedValue),
                                warehouses.Where(wh => wh.FarmId.HasValue && wh.FarmId.Value == f.Id).Count(),
                                henBatches.Where(hb => hb.FarmId.HasValue && hb.FarmId.Value == f.Id).ToList(),
                                availableFormulas.Any(s => s.FarmId.HasValue && s.FarmId.Value == f.Id && s.MaterialContainers != null && s.MaterialContainers.Any()) ? availableFormulas.Where(s => s.FarmId.HasValue && s.FarmId.Value == f.Id).SelectMany(s => s.MaterialContainers.Where(mc => mc.Material.MaterialType.Path == MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula)).Sum(mc => mc.Quantity) : 0
                                )
                );

            var count = fullFarmList.Count();

            if (pageSize > 0)
                fullFarmList = fullFarmList.Skip((page - 1) * pageSize).Take(pageSize);

            return new FullFarmPage()
            {
                Total = count,
                Page = page,
                PageSize = pageSize > 0 ? pageSize : fullFarmList.Count(),
                Items = fullFarmList.ToList()
            };
        }

        /// <summary>
        /// It gets the reception warehouse of a farm. If there is no reception warehouse it creates it 
        /// </summary>
        public async Task<Guid> GetOrCreateReceptionWarehouse(Guid farmId)
        {

            Container receptionWarehouse = containerService.GetAll()
                                         .FirstOrDefault(c => c.FarmId == farmId && c.ContainerType == ContainerTypes.ReceptionWarehouse);
            Guid rwId = Guid.Empty;
            if (receptionWarehouse != null)
                return receptionWarehouse.Id;
            else
            {
                Farm farm = farmService.Get(farmId);
                ReceptionWarehouse rw = new ReceptionWarehouse()
                {
                    AcceptedMaterialType = new List<ContainerMaterialType>() {
                            new ContainerMaterialType(){
                                ActionEnum = ActionsEnum.Store,
                                CapacityStandarizedValue = 0,
                                CapacityUnitId = CapacityUnits.Bird,
                                MaterialTypeId = MaterialTypes.ActivoBiologicoProductivoAve
                            }
                        },
                    AreaContainers = new List<AreaContainer>() {
                            new AreaContainer() {
                                AreaEnum = AreaEnum.Breeding
                            },
                            new AreaContainer(){
                                AreaEnum = AreaEnum.Laying
                            }
                        },
                    Code = farm.Code,
                    CompanyId = farm.CompanyId,
                    Name = this.localizer[Lang.ReceptionWarehouseName],
                    FarmId = farmId
                };
                await containerService.CreateAsync(rw);
                return rw.Id;
            }
        }

        /// <summary>
        /// Get farmDTOs by farms 
        /// </summary>
        public IQueryable<FarmGridView> GetAllForIndex()
        {
            IQueryable<FarmGridView> farms = farmService.GetAllView();
                
            return farms;
        }
    }
}